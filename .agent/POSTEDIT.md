# POST EDIT

- Please commit code with less than 50 words of commit messages
- Push code, and use gh cli to trigger build, which will build image and deploy to dev environment
- Aov<PERSON> pushing to main/master branch, create PR for me.
- Never bypass pre-commit errors, git commit --no-verify is strictly forbidden
- Usually takes 10 min for it to take effect, then you can use argocd commandline to check the deployment status
- For dev env, you can use commandline like `curl -s https://mpp-dev-i.ingka-dt.cn/mcp-server-odi/health | jq '{status, version}'` to check if the latest version is alive, the version contains commit id prefix


