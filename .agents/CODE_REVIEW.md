# Code Review Guidelines

Comprehensive code review guidelines based on Google's engineering practices.

## What to Look For

Always consider [The Standard of Code Review](https://google.github.io/eng-practices/review/reviewer/standard.html) when reviewing each aspect.

## Design

- **Overall design**: Do interactions between code pieces make sense?
- **Codebase fit**: Does this change belong here or in a library?
- **System integration**: Does it integrate well with the rest of the system?
- **Timing**: Is now a good time to add this functionality?

## Functionality

- **Intent vs. implementation**: Does the code do what the developer intended?
- **User benefit**: Is what the developer intended good for users (end-users and future developers)?
- **Edge cases**: Think about edge cases and concurrency problems
- **Bug detection**: Look for bugs visible just by reading the code
- **UI changes**: For user-facing changes, consider requesting a demo

## Complexity

- **Appropriate complexity**: Is the code more complex than it should be?
- **Readability**: Can code readers understand it quickly?
- **Maintainability**: Are developers likely to introduce bugs when modifying this code?
- **Over-engineering**: Avoid generic solutions for current unknown future problems

## Tests

- **Test coverage**: Ask for unit, integration, or end-to-end tests as appropriate
- **Test quality**: Are tests correct, sensible, and useful?
- **Test reliability**: Will tests fail when code is broken? Will they produce false positives?
- **Test maintainability**: Tests are also code that needs maintenance

## Code Quality

- **Naming**: Good names that fully communicate purpose without being too long
- **Comments**: Clear, necessary comments that explain **why**, not **what**
- **Documentation**: Update READMEs and docs when behavior changes
- **Style**: Follow established style guides and maintain consistency
- **Every line**: Review every line of human-written code

## Context & Health

- **Broad context**: Look at the whole file/system, not just the changed lines
- **Code health**: Does this improve or degrade overall system health?
- **Consistency**: Maintain consistency with existing codebase patterns

## Review Principles

- **Encourage good practices**: Compliment developers on things done well
- **Improve code health**: Don't accept changes that degrade system health
- **Understand everything**: If you can't understand the code, ask for clarification
- **Consider the future**: Help future developers who will work with this code

## Review Checklist

Make sure that:
- The code is well-designed
- The functionality is good for the users of the code
- Any UI changes are sensible and look good
- Any parallel programming is done safely
- The code isn't more complex than it needs to be
- The developer isn't implementing things they _might_ need in the future but don't know they need now
- Code has appropriate unit tests
- Tests are well-designed
- The developer used clear names for everything
- Comments are clear and useful, and mostly explain _why_ instead of _what_
- Code is appropriately documented
- The code conforms to style guides

Remember to review **every line** of code, look at the **context**, make sure you're **improving code health**, and compliment developers on **good things** they do.

## Decision Framework

When multiple valid approaches exist in a review, evaluate based on:

1. **Testability** - Can this be easily tested?
2. **Readability** - Will someone understand this in 6 months?
3. **Consistency** - Does this match existing project patterns?
4. **Simplicity** - Is this the simplest solution that works?
5. **Reversibility** - How hard would this be to change later?

## Technical Standards

### Architecture Principles
- **Composition over inheritance** - Prefer dependency injection
- **Interfaces over singletons** - Enable testing and flexibility
- **Explicit over implicit** - Clear data flow and dependencies
- **Test-driven when possible** - Never disable tests, fix them

### Code Quality Standards
- **Single responsibility** per function/class
- **Avoid premature abstractions** - Don't over-engineer
- **No clever tricks** - Choose the boring, obvious solution
- **Clear intent over clever code** - If you need to explain it, it's too complex

### Error Handling
- **Fail fast** with descriptive messages
- **Include context** for debugging
- **Handle errors at appropriate level** - Don't pass the buck
- **Never silently swallow exceptions** - Always log or handle

## Definition of Done Checklist

Ensure the code meets these standards:

- [ ] **Compiles successfully** - No build errors
- [ ] **All tests pass** - Including existing and new tests
- [ ] **Test coverage** - New functionality has appropriate tests
- [ ] **Code quality** - Follows project formatting/linting rules
- [ ] **Clear naming** - Variables, functions, classes have descriptive names
- [ ] **Useful comments** - Explain "why", not "what"
- [ ] **Documentation updated** - READMEs, API docs reflect changes
- [ ] **No shortcuts** - No disabled tests, no `--no-verify` commits
- [ ] **Incremental progress** - Change is appropriately sized
- [ ] **Consistent patterns** - Follows existing codebase conventions
