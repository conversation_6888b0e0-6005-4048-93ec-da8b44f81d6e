# Reference

Under `./readonly` folder, you can refer to a list of project code for various tasks.

Feel free to investigate and deep dive upon necessary.

## MCP(Model Context Protocol) implementation

- mcp-typescript-sdk
- mcp-streamable-http-typescript-server
- fastmcp
- mcp-inspector

## deployment scripts

Helm charts for deployment

- odi-deploy-prod
- orders-deploy-test
- mpp-deploy-test
- kong-mpp-deploy-prod
- kong-mpp-deploy-test

## nacos mse env var config

- mse-config-prod
- mse-config-test

## orders management portal business code

- orders-portal-web

