# Agent Guidance

This folder provides structured guidance for working with the mcp-server-odi codebase using "just enough hierarchy" for complex projects.

## Quick Navigation

### Core Workflow
- [WORKFLOW.md](./WORKFLOW.md) - Complete development workflow (planning → implementation → code review → deployment)
- [IMPLEMENTATION_PLANNING.md](./IMPLEMENTATION_PLANNING.md) - Staged implementation planning for complex features
- [TROUBLESHOOTING.md](./TROUBLESHOOTING.md) - Systematic approach when stuck (3 attempts rule)

### Quality & Standards
- [CODE_REVIEW.md](./CODE_REVIEW.md) - Comprehensive code review guidelines and quality standards

### Reference Materials
- [REFERENCE.md](./REFERENCE.md) - Available reference materials and examples
- [TOOLS.md](./TOOLS.md) - Available CLI and MCP tools

## Development Philosophy

### Core Principles
- **Incremental progress over big bangs** - Small changes that compile and pass tests
- **Learning from existing code** - Study and plan before implementing
- **Clear intent over clever code** - Be boring and obvious
- **Quality is built in, not bolted on** - Tests and reviews are part of the feature

### When You're Stuck
- Follow the **3 attempts rule** - Maximum 3 attempts, then stop and reassess
- Document failures and research alternatives
- Question fundamentals and try different approaches
- See [TROUBLESHOOTING.md](./TROUBLESHOOTING.md) for systematic guidance

## Development Workflow Overview

### 1. Planning Phase
- Fully understand the task before any edits
- For complex work, break into 3-5 stages with clear deliverables
- Study existing patterns in the codebase
- Create implementation plan with testable success criteria

### 2. Implementation Phase
- Follow incremental flow: Understand → Test → Implement → Refactor → Commit
- Make small changes that compile and pass tests
- Commit working code frequently with clear messages
- When stuck, apply the 3 attempts rule

### 3. Code Review Phase
- Review changes every time you make a PR
- Follow comprehensive guidelines for design, functionality, complexity, tests
- Apply decision framework: testability, readability, consistency, simplicity
- Ensure code improves overall system health

### 4. Deployment Phase
- Verify all quality gates are met
- Push and use gh CLI for builds
- Create PRs instead of direct main/master pushes
- Never bypass pre-commit errors
- Verify deployments with curl/argocd

## Quick Start
1. Check [REFERENCE.md](./REFERENCE.md) for relevant examples
2. For complex features, start with [IMPLEMENTATION_PLANNING.md](./IMPLEMENTATION_PLANNING.md)
3. Follow [WORKFLOW.md](./WORKFLOW.md) for your development phase
4. When stuck, use [TROUBLESHOOTING.md](./TROUBLESHOOTING.md)
5. Apply [CODE_REVIEW.md](./CODE_REVIEW.md) standards throughout
