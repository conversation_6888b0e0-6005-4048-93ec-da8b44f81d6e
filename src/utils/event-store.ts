/**
 * In-memory event store implementation for MCP server
 * Provides event storage and replay functionality for resumable connections
 */

import { JSONRPCMessage } from '@modelcontextprotocol/sdk/types.js';

export interface EventStoreStats {
  totalEvents: number;
  totalStreams: number;
  oldestEventTimestamp?: number;
  newestEventTimestamp?: number;
}

export interface StoredEvent {
  id: string;
  streamId: string;
  data: string;
  timestamp: number;
}

export class InMemoryEventStore {
  private events: Map<string, StoredEvent> = new Map();
  private streamEvents: Map<string, string[]> = new Map();
  private eventCounter = 0;

  /**
   * Store an event and return a unique event ID
   * Matches MCP SDK EventStore interface
   */
  async storeEvent(streamId: string, message: JSONRPCMessage): Promise<string> {
    const eventId = `${streamId}-${++this.eventCounter}-${Date.now()}`;
    const event: StoredEvent = {
      id: eventId,
      streamId,
      data: JSON.stringify(message),
      timestamp: Date.now(),
    };

    this.events.set(eventId, event);

    if (!this.streamEvents.has(streamId)) {
      this.streamEvents.set(streamId, []);
    }
    this.streamEvents.get(streamId)!.push(eventId);

    return eventId;
  }

  /**
   * Replay events after a specific event ID
   * Matches MCP SDK EventStore interface
   */
  async replayEventsAfter(
    lastEventId: string,
    { send }: { send: (eventId: string, message: JSONRPCMessage) => Promise<void> }
  ): Promise<string> {
    // Find all events after the lastEventId across all streams
    const allEventIds = Array.from(this.events.keys()).sort();
    const lastIndex = allEventIds.indexOf(lastEventId);

    if (lastIndex === -1) {
      // Event ID not found, replay all events
      for (const eventId of allEventIds) {
        const event = this.events.get(eventId);
        if (event) {
          try {
            const message = JSON.parse(event.data);
            await send(eventId, message);
          } catch {
            // Skip invalid JSON
          }
        }
      }
    } else {
      // Replay events after the last event ID
      const eventsToReplay = allEventIds.slice(lastIndex + 1);
      for (const eventId of eventsToReplay) {
        const event = this.events.get(eventId);
        if (event) {
          try {
            const message = JSON.parse(event.data);
            await send(eventId, message);
          } catch {
            // Skip invalid JSON
          }
        }
      }
    }

    return lastEventId;
  }

  /**
   * Get statistics about the event store
   */
  getStats(): EventStoreStats {
    const allEvents = Array.from(this.events.values());
    const timestamps = allEvents.map(e => e.timestamp);

    return {
      totalEvents: this.events.size,
      totalStreams: this.streamEvents.size,
      oldestEventTimestamp: timestamps.length > 0 ? Math.min(...timestamps) : undefined,
      newestEventTimestamp: timestamps.length > 0 ? Math.max(...timestamps) : undefined,
    };
  }

  /**
   * Clear all events from the store
   */
  clear(): void {
    this.events.clear();
    this.streamEvents.clear();
    this.eventCounter = 0;
  }
}
