.PHONY: help test-fast test-full test-oauth test-oauth-quick test-oauth-disabled test-integration test-integration-blackbox test-integration-oauth2 test-mcp-core test-mcp-core-unit test-mcp-core-blackbox test-ping test-event-store test-tool-annotations clean

help: ## Show help
	@echo "🧪 MCP Server ODI - Integration Tests (Docker-based)"
	@echo "===================================================="
	@echo ""
	@echo "🐳 Docker-Managed Tests (Recommended):"
	@echo "  test-fast              Fast blackbox test (~20s, recommended for dev)"
	@echo "  test-full              Complete blackbox test suite (~2-5min, for CI/CD)"
	@echo "  test-oauth             OAuth test suite (~1-2min, comprehensive OAuth testing)"
	@echo "  test-oauth-quick       Quick OAuth validation (~30s, OAuth ENABLED server)"
	@echo "  test-oauth-disabled    Quick OAuth disabled validation (~30s, OAuth DISABLED server)"
	@echo "  test-integration       All integration tests (~3-4min, Jest + Docker)"
	@echo "  test-integration-blackbox  Jest blackbox integration test (~2min)"
	@echo "  test-integration-oauth2     Jest OAuth2 integration test (~1min)"
	@echo "  clean                  Clean up Docker resources"
	@echo ""
	@echo "📦 Node.js Tests (Server must be running):"
	@echo "  npm run test:oauth:config     OAuth configuration test"
	@echo "  npm run test:oauth:endpoints  OAuth endpoints test"
	@echo "  npm run test:oauth:flow       OAuth flow test"
	@echo ""
	@echo "🚀 MCP Core Feature Tests:"
	@echo "  test-mcp-core                 Run all MCP core feature tests (ping, event-store, annotations)"
	@echo "  test-mcp-core-unit            Run unit tests for MCP core features"
	@echo "  test-mcp-core-blackbox        Run blackbox tests for MCP core features"
	@echo ""
	@echo "🔧 Individual Feature Tests:"
	@echo "  test-ping                     Test MCP ping implementation and health endpoints"
	@echo "  test-event-store              Test event store and resumability features"
	@echo "  test-tool-annotations         Test tool annotation and classification logic"
	@echo ""
	@echo "💡 Tip: Use 'make' commands for full automation, 'npm run' for individual tests"

# Fast test - just run the working MCP client test
test-fast: ## Run fast MCP client test
	@echo "⚡ Running fast blackbox test..."
	@docker-compose -f blackbox/config/docker-compose.test.yml up mcp-server-oauth2-disabled --detach
	@sleep 10
	@MCP_SERVER_URL=http://localhost:3000 node blackbox/tests/mcp-client-simulator.js
	@docker-compose -f blackbox/config/docker-compose.test.yml down

# Full test suite using Docker Compose
test-full: ## Run full Docker test suite
	@echo "🧪 Running full test suite..."
	@docker-compose -f blackbox/config/docker-compose.test.yml up --build --abort-on-container-exit
	@docker-compose -f blackbox/config/docker-compose.test.yml down -v --remove-orphans

# OAuth test suite
test-oauth: ## Run comprehensive OAuth test suite
	@echo "🔐 Running OAuth test suite..."
	@echo "Starting both OAuth-enabled and OAuth-disabled servers..."
	@docker-compose -f blackbox/config/docker-compose.test.yml up mcp-server-oauth2-enabled mcp-server-oauth2-disabled --detach
	@sleep 15
	@./blackbox/scripts/run-oauth-tests.sh http://localhost:3001 http://localhost:3000
	@docker-compose -f blackbox/config/docker-compose.test.yml down

# Quick OAuth validation
test-oauth-quick: ## Run quick OAuth validation tests
	@echo "⚡ Running quick OAuth validation..."
	@docker-compose -f blackbox/config/docker-compose.test.yml up mcp-server-oauth2-enabled --detach
	@sleep 15
	@MCP_SERVER_PORT=3001 MCP_SERVER_URL=http://localhost:3001 node blackbox/tests/quick-auth-test.js
	@MCP_SERVER_URL=http://localhost:3001 node blackbox/tests/oauth-server-blackbox-test.js
	@docker-compose -f blackbox/config/docker-compose.test.yml down

# OAuth disabled validation (for comparison)
test-oauth-disabled: ## Run OAuth disabled validation tests
	@echo "🚫 Running OAuth disabled validation..."
	@docker-compose -f blackbox/config/docker-compose.test.yml up mcp-server-oauth2-disabled --detach
	@sleep 10
	@MCP_SERVER_PORT=3000 MCP_SERVER_URL=http://localhost:3000 node blackbox/tests/quick-auth-test.js
	@MCP_SERVER_URL=http://localhost:3000 node blackbox/tests/oauth-server-blackbox-test.js
	@docker-compose -f blackbox/config/docker-compose.test.yml down

# Integration tests with Docker management
test-integration: ## Run all Jest integration tests with Docker
	@echo "🧪 Running all integration tests..."
	@$(MAKE) test-integration-blackbox
	@$(MAKE) test-integration-oauth2

test-integration-blackbox: ## Run Jest blackbox integration test
	@echo "📦 Running Jest blackbox integration test..."
	@docker-compose -f blackbox/config/docker-compose.test.yml up mcp-server-oauth2-disabled --detach
	@sleep 10
	@MCP_SERVER_URL=http://localhost:3000 npx jest tests/integration/mcp-blackbox.test.ts --testTimeout=300000 --testPathIgnorePatterns=[] || true
	@docker-compose -f blackbox/config/docker-compose.test.yml down

test-integration-oauth2: ## Run Jest OAuth2 integration test
	@echo "🔐 Running Jest OAuth2 integration test..."
	@docker-compose -f blackbox/config/docker-compose.test.yml up mcp-server-oauth2-enabled --detach
	@sleep 15
	@MCP_SERVER_URL=http://localhost:3001 npx jest tests/integration/mcp-oauth2-client.test.ts --testTimeout=180000 --testPathIgnorePatterns=[] || true
	@docker-compose -f blackbox/config/docker-compose.test.yml down

# Clean up Docker resources
clean: ## Clean up Docker resources
	@echo "🧹 Cleaning up..."
	@docker-compose -f blackbox/config/docker-compose.test.yml down -v --remove-orphans 2>/dev/null || true
	@docker network rm mcp-test-network 2>/dev/null || true
	@docker system prune -f --volumes 2>/dev/null || true

# Development utilities
logs: ## Show Docker service logs
	@docker-compose -f blackbox/config/docker-compose.test.yml logs

status: ## Show Docker service status
	@docker-compose -f blackbox/config/docker-compose.test.yml ps

summary: ## Show available commands
	@echo "📊 MCP Server ODI - Available Commands"
	@echo "====================================="
	@echo ""
	@echo "🐳 Docker-Managed Tests (Full Automation):"
	@echo "  make test-fast                    # Quick blackbox test (~20s)"
	@echo "  make test-full                    # Complete test suite (~2-5min)"
	@echo "  make test-oauth                   # OAuth test suite (~1-2min)"
	@echo "  make test-oauth-quick             # Quick OAuth validation (~30s, OAuth ENABLED)"
	@echo "  make test-oauth-disabled          # Quick OAuth disabled validation (~30s, OAuth DISABLED)"
	@echo "  make test-integration             # All Jest integration tests (~3-4min)"
	@echo "  make test-integration-blackbox    # Jest blackbox test (~2min)"
	@echo "  make test-integration-oauth2      # Jest OAuth2 test (~1min)"
	@echo ""
	@echo "📦 Node.js Tests (Manual Server Management):"
	@echo "  npm run test:unit                 # Unit tests only (no server needed)"
	@echo "  npm run test:integration:simple   # Simple integration test"
	@echo "  npm run test:oauth:config         # OAuth configuration validation"
	@echo "  npm run test:oauth:endpoints      # OAuth endpoints testing"
	@echo "  npm run test:oauth:flow           # OAuth flow testing"
	@echo "  make clean        # Clean up Docker resources"
	@echo ""
	@echo "Development:"
	@echo "  make logs         # Show service logs"
	@echo "  make status       # Show service status"
	@echo "  make help         # Show this help"
	@echo ""
	@echo "Unit Tests:"
	@echo "  npm test          # Show testing guidance"
	@echo "  npm run test:unit # Run unit tests"

# MCP Core Feature Tests
test-mcp-core: ## Run all MCP core feature tests (ping, event-store, tool-annotations)
	@echo "🚀 Running all MCP core feature tests..."
	@npm run test:mcp-core

test-mcp-core-unit: ## Run unit tests for MCP core features
	@echo "🧪 Running MCP core feature unit tests..."
	@npm run test:mcp-core:unit

test-mcp-core-blackbox: ## Run blackbox tests for MCP core features (requires running server)
	@echo "🔍 Running MCP core feature blackbox tests..."
	@echo "⚠️  Note: Server must be running on localhost:8080"
	@npm run test:mcp-core:blackbox

# Individual Feature Tests
test-ping: ## Test MCP ping implementation and health endpoints
	@echo "🏓 Testing MCP ping and health endpoints..."
	@npx jest tests/transport/http-transport.test.ts --testNamePattern="Ping|Health|Ready"

test-event-store: ## Test event store and resumability features
	@echo "💾 Testing event store and resumability..."
	@npx jest tests/utils/event-store.test.ts

test-tool-annotations: ## Test tool annotation and classification logic
	@echo "🏷️  Testing tool annotations and classification..."
	@npx jest tests/server/tool-annotations.test.ts

# Clean up Docker resources
clean: ## Clean up Docker resources
	@echo "🧹 Cleaning up Docker resources..."
	@docker-compose -f blackbox/config/docker-compose.test.yml down -v --remove-orphans
	@docker system prune -f --volumes
