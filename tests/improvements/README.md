# MCP Core Features Tests

This directory contains tests for the core MCP protocol features implemented based on the official MCP TypeScript SDK examples.

## 🎯 **Long-Term Test Strategy**

This test suite is organized by **feature domains** rather than implementation phases, ensuring maintainability over time:

- **`test-mcp-core`** - All core MCP protocol features
- **`test-ping`** - MCP ping implementation and health monitoring
- **`test-event-store`** - Event store and resumability features
- **`test-tool-annotations`** - Tool classification and metadata

## Test Structure

### Unit Tests (`/tests/`)
- **`utils/event-store.test.ts`** - Tests for the InMemoryEventStore implementation
- **`server/tool-annotations.test.ts`** - Tests for modern tool registration with annotations
- **`transport/http-transport.test.ts`** - Enhanced tests for HTTP transport improvements

### Blackbox Tests (`/blackbox/`)
- **`tests/mcp-core-blackbox-test.js`** - Node.js blackbox tests for end-to-end validation
- **`scripts/test-mcp-core.sh`** - Shell script for comprehensive blackbox testing

## What's Being Tested

### 1. Event Store Functionality
- Event storage and retrieval
- Event replay after specific event IDs
- Stream isolation (events from different streams don't interfere)
- Statistics tracking (total events, stream count)
- Clear functionality for testing

### 2. Enhanced HTTP Transport
- Improved health endpoints with session and ping information
- Ready endpoint following FastMCP patterns
- Ping configuration and monitoring
- Session management and cleanup
- Event store integration

### 3. Tool Annotations
- Modern tool registration with annotations
- Proper tool categorization (readOnly, openWorld, destructive hints)
- Tool titles and enhanced descriptions
- Structured output with metadata

### 4. Ping Implementation
- Real MCP ping messages (not just logging)
- Configurable ping intervals
- Automatic session cleanup on ping failures
- Health monitoring integration

### 5. End-to-End Integration
- MCP protocol initialization with event store
- Tool listing with annotations
- Tool execution with structured responses
- Session monitoring during operations

## Running the Tests

### All MCP Core Tests
```bash
# Run both unit and blackbox tests
make test-mcp-core
# or
npm run test:mcp-core
```

### Unit Tests Only
```bash
# Run unit tests (no server needed)
make test-mcp-core-unit
# or
npm run test:mcp-core:unit
```

### Blackbox Tests Only
```bash
# Start the server first
TRANSPORT=http npm run dev

# Then run blackbox tests (in another terminal)
make test-mcp-core-blackbox
# or
npm run test:mcp-core:blackbox
```

### Individual Feature Tests
```bash
# Test specific features independently
make test-ping              # MCP ping and health endpoints
make test-event-store       # Event store and resumability
make test-tool-annotations  # Tool classification logic

# Or via npm
npm run test:ping
npm run test:event-store
npm run test:tool-annotations
```

### Individual Test Files
```bash
# Test event store only
npx jest tests/utils/event-store.test.ts

# Test tool annotations only
npx jest tests/server/tool-annotations.test.ts

# Test HTTP transport improvements only
npx jest tests/transport/http-transport.test.ts
```

## Test Coverage

### Unit Tests Cover:
- ✅ Event store storage and replay logic
- ✅ Tool annotation classification logic
- ✅ HTTP transport configuration
- ✅ Health endpoint response structure
- ✅ Ping configuration management

### Blackbox Tests Cover:
- ✅ Health endpoints (`/health`, `/ready`, `/mcp/ping`)
- ✅ MCP initialization with event store
- ✅ Tool listing with annotations
- ✅ Tool execution with structured responses
- ✅ Session monitoring and statistics
- ✅ End-to-end protocol compliance

## Expected Test Results

### Successful Unit Test Output:
```
PASS tests/utils/event-store.test.ts
PASS tests/server/tool-annotations.test.ts  
PASS tests/transport/http-transport.test.ts

Test Suites: 3 passed, 3 total
Tests: 25+ passed, 25+ total
```

### Successful Blackbox Test Output:
```
🚀 Starting MCP Server Improvements Blackbox Tests
==============================================

--- Running Health Endpoints Test ---
✅ /health endpoint validation passed
✅ /ready endpoint validation passed  
✅ /mcp/ping endpoint validation passed

--- Running MCP Initialization Test ---
✅ MCP initialization successful

--- Running Tool Annotations Test ---
✅ Found tools with annotations

--- Running Ping Tool Test ---
✅ Ping tool execution successful
✅ Ping tool returned structured content

--- Running Session Monitoring Test ---
✅ Session monitoring completed

=== Test Results Summary ===
Passed: 5/5
🎉 All blackbox tests completed successfully!
```

## Troubleshooting

### Common Issues:

1. **Server not running for blackbox tests**
   ```bash
   # Start server first
   TRANSPORT=http npm run dev
   ```

2. **Port conflicts**
   ```bash
   # Check if port 8080 is in use
   lsof -i :8080
   ```

3. **Missing dependencies**
   ```bash
   # Install test dependencies
   npm install
   ```

4. **Jest configuration issues**
   ```bash
   # Run with verbose output
   npx jest --verbose tests/utils/event-store.test.ts
   ```

### Debug Mode:
```bash
# Run blackbox tests with debug output
DEBUG=1 node blackbox/tests/improvements-blackbox-test.js
```

## Integration with CI/CD

These tests are designed to integrate with your existing CI/CD pipeline:

```yaml
# Example GitHub Actions step
- name: Test MCP Improvements
  run: |
    npm run build
    npm run test:improvements:unit
    # Start server in background for blackbox tests
    TRANSPORT=http npm run dev &
    sleep 10
    npm run test:improvements:blackbox
```

## Contributing

When adding new improvements:

1. Add unit tests for the core logic
2. Add blackbox tests for end-to-end validation
3. Update this README with new test descriptions
4. Ensure tests pass in both development and CI environments
